@import './variables.less';

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #app {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: @text-color;
  background-color: @bg-color;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
}

// 通用工具类
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pointer {
  cursor: pointer;
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity @transition-time @ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 全局卡片蓝色光圈效果
.card-blue-glow-global {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #1890ff, #40a9ff, #69c0ff, #91d5ff);
    border-radius: inherit;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
    filter: blur(8px);
  }

  &:hover::before {
    opacity: 0.6;
  }
}
