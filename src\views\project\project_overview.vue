<script setup lang="ts">
import PageLayout from "@/layouts/PageLayout.vue";
import ProjectFormModal from "@/components/project/ProjectFormModal.vue";
import "@/assets/styles/card.less";
import type { Project, StatCard } from "@/types";
import {
  createProjectWithProgress,
  getProjectStatusText,
  getProjectTypeText
} from "@/utils/project";

const message = useMessage();
const dialog = useDialog();
const router = useRouter();

// 模态框状态
const showCreateModal = ref(false);
const showEditModal = ref(false);
const editingProject = ref<Partial<Project>>({});

// 项目统计
const stats = ref<StatCard[]>([
  { title: "进行中", value: 12, color: "#2d8cf0", trend: "up", trendValue: 8 },
  { title: "已完成", value: 45, color: "#19be6b", trend: "up", trendValue: 12 },
  { title: "已延期", value: 5, color: "#ed4014", trend: "down", trendValue: 5 },
  {
    title: "总项目数",
    value: 62,
    color: "#ff9900",
    trend: "up",
    trendValue: 15,
  },
]);

const activeTab = ref("follow");

// 项目数据
const projectList = ref<Project[]>([
  createProjectWithProgress({
    id: 1,
    name: "PPMS项目管理系统",
    cover: "https://p3-pc.douyinpic.com/obj/ies-music/ies-music-common-bv/acp/playlist_v2/PlayListV3/static/cover.png",
    version: "v1.0.0",
    manager: "张三",
    type: "new project",
    priority: "high",
    status: "development",
    starred: true,
    archived: false,
    tags: ["Vue3", "TypeScript"],
    startDate: Date.now() - 30 * 24 * 60 * 60 * 1000,
    endDate: Date.now() + 60 * 24 * 60 * 60 * 1000,
    createdAt: Date.now() - 30 * 24 * 60 * 60 * 1000,
    updatedAt: Date.now(),
  }),
  createProjectWithProgress({
    id: 2,
    name: "企业官网改版",
    cover: "https://p3-pc.douyinpic.com/obj/ies-music/ies-music-common-bv/acp/playlist_v2/PlayListV3/static/cover.png",
    version: "v2.1.0",
    manager: "李四",
    type: "cr",
    priority: "medium",
    status: "uat",
    starred: false,
    archived: false,
    tags: ["设计", "UI/UX"],
    startDate: Date.now() - 15 * 24 * 60 * 60 * 1000,
    endDate: Date.now() + 45 * 24 * 60 * 60 * 1000,
    createdAt: Date.now() - 15 * 24 * 60 * 60 * 1000,
    updatedAt: Date.now(),
  }),
  createProjectWithProgress({
    id: 3,
    name: "移动端APP开发",
    cover: "https://p3-pc.douyinpic.com/obj/ies-music/ies-music-common-bv/acp/playlist_v2/PlayListV3/static/cover.png",
    version: "v1.5.0",
    manager: "王五",
    type: "new project",
    priority: "high",
    status: "inner test",
    starred: true,
    archived: false,
    tags: ["React Native", "移动端"],
    startDate: Date.now() - 60 * 24 * 60 * 60 * 1000,
    endDate: Date.now() + 15 * 24 * 60 * 60 * 1000,
    createdAt: Date.now() - 60 * 24 * 60 * 60 * 1000,
    updatedAt: Date.now(),
  }),
  createProjectWithProgress({
    id: 4,
    name: "数据中台建设",
    cover: "", // 空字符串测试默认背景
    version: "v0.5.0",
    manager: "赵六",
    type: "cr",
    priority: "medium",
    status: "planning",
    starred: false,
    archived: false,
    tags: ["大数据", "ETL"],
    startDate: Date.now() - 20 * 24 * 60 * 60 * 1000,
    endDate: Date.now() + 90 * 24 * 60 * 60 * 1000,
    createdAt: Date.now() - 20 * 24 * 60 * 60 * 1000,
    updatedAt: Date.now(),
  }),
  createProjectWithProgress({
    id: 5,
    name: "AI智能客服系统",
    cover: "https://invalid-url.com/image.jpg", // 无效URL测试错误处理
    version: "v1.0.0",
    manager: "王七",
    type: "new project",
    priority: "urgent",
    status: "go live",
    starred: true,
    archived: false,
    tags: ["AI", "NLP", "客服"],
    startDate: Date.now() - 10 * 24 * 60 * 60 * 1000,
    endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
    createdAt: Date.now() - 10 * 24 * 60 * 60 * 1000,
    updatedAt: Date.now(),
  }),
]);

const handleCreateProject = () => {
  showCreateModal.value = true;
};

// 跳转到项目详情页
const goToProjectDetail = (projectId: string | number) => {
  router.push({ name: "ProjectDetail", params: { id: projectId } });
};

const toggleStar = (id: string | number) => {
  const project = projectList.value.find((p) => p.id === id);
  if (project) {
    project.starred = !project.starred;
    // 这里可以添加保存到后端的逻辑
    message.success(project.starred ? "已添加到关注" : "已取消关注");
  }
};

// 编辑项目
const handleEditProject = (projectId: string | number) => {
  const project = projectList.value.find((p) => p.id === projectId);
  if (project) {
    editingProject.value = { ...project };
    showEditModal.value = true;
  }
};

// 删除项目
const handleDeleteProject = (projectId: string | number) => {
  const project = projectList.value.find((p) => p.id === projectId);
  if (project) {
    dialog.warning({
      title: '确认删除',
      content: `确定要删除项目"${project.name}"吗？此操作不可恢复。`,
      positiveText: '确定删除',
      negativeText: '取消',
      onPositiveClick: () => {
        // 执行删除操作
        const index = projectList.value.findIndex(p => p.id === projectId);
        if (index > -1) {
          projectList.value.splice(index, 1);
          message.success('项目删除成功');
        }
      }
    });
  }
};

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  const projectCard = img.closest('.project-card');
  if (projectCard) {
    img.style.display = 'none';
  }
};

// 图片加载成功处理
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.style.display = 'block';
};

// 处理创建项目成功
const handleCreateSuccess = (projectData: Partial<Project>) => {
  // 生成新的项目ID
  const newId = Math.max(...projectList.value.map(p => Number(p.id))) + 1;
  const newProject = createProjectWithProgress({
    ...projectData,
    id: newId,
    starred: false,
    archived: false,
    status: 'not_started',
    createdAt: Date.now(),
    updatedAt: Date.now(),
  } as Project);

  projectList.value.unshift(newProject);
};

// 处理编辑项目成功
const handleEditSuccess = (projectData: Partial<Project>) => {
  const index = projectList.value.findIndex(p => p.id === editingProject.value.id);
  if (index > -1) {
    projectList.value[index] = createProjectWithProgress({
      ...projectList.value[index],
      ...projectData,
      updatedAt: Date.now(),
    });
  }
};
</script>

<template>
  <page-layout title="项目概览">
    <!-- 统计卡片 -->
    <n-grid :x-gap="16" :y-gap="16" :cols="4" class="stats-grid">
      <n-gi v-for="(stat, index) in stats" :key="index">
        <n-card>
          <n-statistic :label="stat.title" :value="stat.value">
            <template #suffix>
              <n-icon
                :color="stat.trend === 'up' ? '#19be6b' : '#ed4014'"
                size="16"
                style="margin-left: 4px"
              >
                <IconAntDesignArrowUpOutlined v-if="stat.trend === 'up'" />
                <IconAntDesignArrowDownOutlined v-else />
              </n-icon>
              <span
                :style="{ color: stat.trend === 'up' ? '#19be6b' : '#ed4014' }"
              >
                {{ stat.trendValue }}%
              </span>
            </template>
          </n-statistic>
          <n-progress
            :percentage="typeof stat.value === 'number' ? stat.value : 0"
            :show-indicator="false"
            :height="4"
            :color="stat.color"
            :rail-style="{ marginTop: '12px' }"
          />
        </n-card>
      </n-gi>
    </n-grid>

    <!-- 项目列表 -->
    <n-card class="project-list-card">
      <n-tabs v-model:value="activeTab" type="line" animated>
        <n-tab-pane name="follow" tab="已关注">
          <div class="project-grid">
            <!-- 项目卡片 -->
            <div
              v-for="project in projectList.filter((p) => p.starred)"
              :key="`starred-${project.id}`"
              class="project-card"
              @click="goToProjectDetail(project.id)"
              style="cursor: pointer"
            >
              <div class="project-cover">
                <img
                  :src="project.cover"
                  :alt="project.name"
                  @error="handleImageError"
                  @load="handleImageLoad"
                />
                <div class="project-cover-fallback">
                  <div class="fallback-content">
                    <n-icon size="48" color="#ccc">
                      <IconAntDesignProjectOutlined />
                    </n-icon>
                    <span class="fallback-text">{{ project.name }}</span>
                  </div>
                </div>
                <div class="project-star" @click.stop="toggleStar(project.id)">
                  <n-icon
                    :size="20"
                    :color="project.starred ? '#ffcc00' : '#8c8c8c'"
                  >
                    <IconAntDesignStarFilled v-if="project.starred" />
                    <IconAntDesignStarOutlined v-else />
                  </n-icon>
                </div>
              </div>
              <div class="project-info">
                <div class="project-header">
                  <n-ellipsis class="project-name" :line-clamp="1">
                    {{ project.name }}
                  </n-ellipsis>
                  <n-tag size="small" type="info" round>
                    {{ project.version }}
                  </n-tag>
                </div>
                <div class="project-meta">
                  <div class="meta-item">
                    <n-icon size="14"><IconAntDesignUserOutlined /></n-icon>
                    <span>{{ project.manager }}</span>
                  </div>
                  <div class="meta-item">
                    <n-icon size="14"><IconAntDesignBookOutlined /></n-icon>
                    <span>{{ getTypeDisplay(project.type) }}</span>
                  </div>
                </div>
                <div class="project-status">
                  <n-tag size="small" :type="getStatusType(project.status)">
                    {{ getStatusDisplay(project.status) }}
                  </n-tag>
                  <n-progress
                    type="line"
                    :percentage="project.progress || 0"
                    :height="6"
                    :border-radius="3"
                    :fill-border-radius="3"
                    :show-indicator="false"
                    :color="getProgressColor(project.progress || 0)"
                    style="flex: 1; margin-left: 8px"
                  />
                  <span class="progress-text">{{ project.progress || 0 }}%</span>
                </div>
              </div>

              <!-- 悬浮遮罩层 -->
              <div class="project-overlay">
                <!-- 查看按钮 -->
                <div class="project-view-action">
                  <n-button
                    size="large"
                    type="primary"
                    circle
                    @click.stop="goToProjectDetail(project.id)"
                  >
                    <template #icon>
                      <n-icon size="20"><IconAntDesignEyeOutlined /></n-icon>
                    </template>
                  </n-button>
                  <span class="view-text">查看详情</span>
                </div>

                <!-- 底部操作按钮 -->
                <div class="project-actions">
                  <n-button
                    size="small"
                    quaternary
                    @click.stop="handleEditProject(project.id)"
                    class="action-btn edit-btn"
                  >
                    <template #icon>
                      <n-icon><IconAntDesignEditOutlined /></n-icon>
                    </template>
                    编辑
                  </n-button>
                  <n-button
                    size="small"
                    quaternary
                    @click.stop="handleDeleteProject(project.id)"
                    class="action-btn delete-btn"
                  >
                    <template #icon>
                      <n-icon><IconAntDesignDeleteOutlined /></n-icon>
                    </template>
                    删除
                  </n-button>
                </div>
              </div>
            </div>

            <!-- 新建项目卡片 -->
            <div class="project-card new-project" @click="handleCreateProject">
              <div class="new-project-content">
                <n-icon :size="32" color="#8c8c8c">
                  <IconAntDesignPlusOutlined />
                </n-icon>
                <div class="new-project-text">新建项目</div>
              </div>
            </div>
          </div>
        </n-tab-pane>
        <n-tab-pane name="in-progress" tab="进行中">
          <div class="empty-tab">
            <n-text depth="3">暂无进行中的项目</n-text>
          </div>
        </n-tab-pane>
        <n-tab-pane name="completed" tab="已完成">
          <div class="empty-tab">
            <n-text depth="3">暂无已完成的项目</n-text>
          </div>
        </n-tab-pane>
        <n-tab-pane name="archived" tab="已归档">
          <div class="empty-tab">
            <n-text depth="3">暂无已归档的项目</n-text>
          </div>
        </n-tab-pane>
        <n-tab-pane name="all" tab="全部项目">
          <div class="project-grid">
            <!-- 项目卡片 -->
            <div
              v-for="project in projectList"
              :key="`all-${project.id}`"
              class="project-card"
              @click="goToProjectDetail(project.id)"
              style="cursor: pointer"
            >
              <div class="project-cover">
                <img
                  :src="project.cover"
                  :alt="project.name"
                  @error="handleImageError"
                  @load="handleImageLoad"
                />
                <div class="project-cover-fallback">
                  <div class="fallback-content">
                    <n-icon size="48" color="#ccc">
                      <IconAntDesignProjectOutlined />
                    </n-icon>
                    <span class="fallback-text">{{ project.name }}</span>
                  </div>
                </div>
                <div class="project-star" @click.stop="toggleStar(project.id)">
                  <n-icon
                    :size="20"
                    :color="project.starred ? '#ffcc00' : '#8c8c8c'"
                  >
                    <IconAntDesignStarFilled v-if="project.starred" />
                    <IconAntDesignStarOutlined v-else />
                  </n-icon>
                </div>
              </div>
              <div class="project-info">
                <div class="project-header">
                  <n-ellipsis class="project-name" :line-clamp="1">
                    {{ project.name }}
                  </n-ellipsis>
                  <n-tag size="small" type="info" round>
                    {{ project.version }}
                  </n-tag>
                </div>
                <div class="project-meta">
                  <div class="meta-item">
                    <n-icon size="14"><IconAntDesignUserOutlined /></n-icon>
                    <span>{{ project.manager }}</span>
                  </div>
                  <div class="meta-item">
                    <n-icon size="14"><IconAntDesignBookOutlined /></n-icon>
                    <span>{{ getTypeDisplay(project.type) }}</span>
                  </div>
                </div>
                <div class="project-status">
                  <n-tag size="small" :type="getStatusType(project.status)">
                    {{ getStatusDisplay(project.status) }}
                  </n-tag>
                  <n-progress
                    type="line"
                    :percentage="project.progress || 0"
                    :height="6"
                    :border-radius="3"
                    :fill-border-radius="3"
                    :show-indicator="false"
                    :color="getProgressColor(project.progress || 0)"
                    style="flex: 1; margin-left: 8px"
                  />
                  <span class="progress-text">{{ project.progress || 0 }}%</span>
                </div>
              </div>

              <!-- 悬浮遮罩层 -->
              <div class="project-overlay">
                <!-- 查看按钮 -->
                <div class="project-view-action">
                  <n-button
                    size="large"
                    type="primary"
                    circle
                    @click.stop="goToProjectDetail(project.id)"
                  >
                    <template #icon>
                      <n-icon size="20"><IconAntDesignEyeOutlined /></n-icon>
                    </template>
                  </n-button>
                  <span class="view-text">查看详情</span>
                </div>

                <!-- 底部操作按钮 -->
                <div class="project-actions">
                  <n-button
                    size="small"
                    quaternary
                    @click.stop="handleEditProject(project.id)"
                    class="action-btn edit-btn"
                  >
                    <template #icon>
                      <n-icon><IconAntDesignEditOutlined /></n-icon>
                    </template>
                    编辑
                  </n-button>
                  <n-button
                    size="small"
                    quaternary
                    @click.stop="handleDeleteProject(project.id)"
                    class="action-btn delete-btn"
                  >
                    <template #icon>
                      <n-icon><IconAntDesignDeleteOutlined /></n-icon>
                    </template>
                    删除
                  </n-button>
                </div>
              </div>
            </div>

            <!-- 新建项目卡片 -->
            <div class="project-card new-project" @click="handleCreateProject">
              <div class="new-project-content">
                <n-icon :size="32" color="#8c8c8c">
                  <IconAntDesignPlusOutlined />
                </n-icon>
                <div class="new-project-text">新建项目</div>
              </div>
            </div>
          </div>
        </n-tab-pane>
      </n-tabs>
    </n-card>

    <!-- 项目表单模态框 -->
    <ProjectFormModal
      v-model:show="showCreateModal"
      mode="create"
      @success="handleCreateSuccess"
    />

    <ProjectFormModal
      v-model:show="showEditModal"
      mode="edit"
      :project-data="editingProject"
      @success="handleEditSuccess"
    />
  </page-layout>
</template>

<script lang="ts">
import type { ProjectStatus } from "@/types/project";

function getStatusType(status: ProjectStatus) {
  switch (status) {
    case "development":
      return "primary";
    case "go live":
      return "success";
    case "request collect":
      return "default";
    case "planning":
      return "info";
    case "tab":
    case "uat":
    case "cab":
      return "warning";
    case "inner test":
      return "info";
    default:
      return "default";
  }
}

function getProgressColor(progress: number) {
  if (progress > 80) return "#19be6b";
  if (progress > 50) return "#2d8cf0";
  return "#ff9900";
}

function getStatusDisplay(status: ProjectStatus) {
  return getProjectStatusText(status);
}

function getTypeDisplay(type: string) {
  return getProjectTypeText(type);
}
</script>

<style lang="less" scoped>
@import "@/assets/styles/card.less";

.stats-grid {
  margin-bottom: 24px;
  width: 100%;

  :deep(.n-gi) {
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-4px);
    }
  }

  :deep(.n-card) {
    .card-base();
    .card-blue-glow();
    height: 100%;

    .n-card__content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: left;
    }

    .n-statistic {
      .n-statistic-value {
        font-size: 24px;
        font-weight: 600;
        text-align: left;
      }

      .n-statistic-label {
        font-size: 13px;
        color: #666;
        text-align: left;
      }
    }
  }
}

.project-list-card {
  .card-base();
  .card-blue-glow();
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;

  :global(.n-card__content) {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0;
    min-height: 0;
  }

  :global(.n-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  :global(.n-tabs-nav) {
    padding: 0 24px;
    margin: 0;
    background: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    flex-shrink: 0;
  }

  :global(.n-tabs-nav .n-tabs-tab) {
    padding: 16px 0;
    margin: 0 16px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.2s;
  }

  :global(.n-tabs-nav .n-tabs-tab.n-tabs-tab--active) {
    color: var(--primary-color);
    font-weight: 600;
  }

  :global(.n-tabs-nav .n-tabs-tab:hover) {
    color: var(--primary-color);
  }

  :global(.n-tabs-ink-bar) {
    height: 3px;
    border-radius: 2px 2px 0 0;
  }

  :global(.n-tabs-content) {
    flex: 1;
    min-height: 0;
  }

  :global(.n-tab-pane) {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
  }
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 8px;
  width: 100%;
  box-sizing: border-box;
}

.project-card {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #fff;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #52c41a);
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #1890ff, #40a9ff, #69c0ff, #91d5ff);
    border-radius: inherit;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
    filter: blur(8px);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border-color: rgba(24, 144, 255, 0.2);

    &::before {
      opacity: 1;
    }

    &::after {
      opacity: 0.6;
    }

    .project-overlay {
      opacity: 1;
      visibility: visible;
    }
  }
}

.new-project {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 180px;
  border: 2px dashed #e0e0e0;
  background: #fafafa;
  transition: all 0.3s;
  border-radius: 12px;

  &:hover {
    border-color: var(--primary-color);
    background: rgba(24, 144, 255, 0.02);
    color: var(--primary-color);

    .new-project-content {
      transform: scale(1.05);
    }
  }

  .new-project-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    transition: transform 0.3s;

    .n-icon {
      transition: all 0.3s;
    }
  }

  .new-project-text {
    color: #8c8c8c;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s;
  }
}

.project-cover {
  position: relative;
  height: 140px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
    opacity: 0.8;
    transition: opacity 0.3s;
    z-index: 2;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
  }

  .project-card:hover & {
    &::after {
      opacity: 0.6;
    }

    img {
      transform: scale(1.08);
    }
  }
}

// 默认背景样式
.project-cover-fallback {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 0;

  .fallback-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    text-align: center;
    padding: 20px;

    .fallback-text {
      color: white;
      font-size: 14px;
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      line-height: 1.4;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .n-icon {
      opacity: 0.8;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    }
  }
}

.project-star {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.9);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 2;
  transition: all 0.3s;

  &:hover {
    background: #fff;
    transform: scale(1.1);

    .n-icon {
      color: #ffcc00 !important;
    }
  }

  .n-icon {
    transition: all 0.3s;
  }
}

.project-info {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;

  .project-name {
    font-weight: 600;
    color: #1f2d3d;
    font-size: 15px;
    margin: 0;
    flex: 1;
    min-width: 0;
    line-height: 1.4;
    text-align: left;
  }

  .n-tag {
    font-size: 11px;
    height: 20px;
    line-height: 18px;
    border-radius: 10px;
    padding: 0 8px;
    flex-shrink: 0;
  }
}

.project-meta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #666;
  margin: 4px 0;

  .meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 0;

    .n-icon {
      color: #999;
      font-size: 14px;
      flex-shrink: 0;
    }

    span {
      line-height: 1;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.project-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding-top: 12px;
  border-top: 1px dashed #f0f0f0;

  .n-tag {
    height: 22px;
    line-height: 20px;
    font-size: 12px;
    font-weight: 500;
    padding: 0 8px;
  }

  .n-progress {
    flex: 1;

    :deep(.n-progress-rail) {
      background-color: #f5f5f5;
    }
  }

  .progress-text {
    font-size: 12px;
    color: #8c8c8c;
    min-width: 36px;
    text-align: right;
    font-weight: 500;
    font-feature-settings: "tnum";
  }
}

// 悬浮遮罩层
.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(2px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px 16px;
  border-radius: 12px;
}

// 查看按钮区域
.project-view-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: center;

  .n-button {
    width: 56px;
    height: 56px;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
    }
  }

  .view-text {
    color: white;
    font-size: 14px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

// 底部操作按钮
.project-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  width: 100%;

  .action-btn {
    flex: 1;
    font-size: 12px;
    height: 36px;
    border-radius: 8px;
    font-weight: 500;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    }

    // 编辑按钮样式
    &.edit-btn {
      background: rgba(24, 144, 255, 0.8);

      &:hover {
        background: rgba(24, 144, 255, 0.9);
        border-color: rgba(255, 255, 255, 0.4);
      }
    }

    // 删除按钮样式
    &.delete-btn {
      background: rgba(245, 63, 63, 0.8);

      &:hover {
        background: rgba(245, 63, 63, 0.9);
        border-color: rgba(255, 255, 255, 0.4);
      }
    }

    :deep(.n-button__content) {
      color: white;
    }

    :deep(.n-icon) {
      color: white;
    }
  }
}

.empty-tab {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
  gap: 16px;

  .n-icon {
    font-size: 48px;
    opacity: 0.6;
    margin-bottom: 8px;
  }

  .n-text {
    font-size: 14px;
  }

  .n-button {
    margin-top: 16px;
  }
}
</style>
