import { defineConfig, presetUno, presetAttributify, presetIcons } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
    }),
  ],
  shortcuts: {
    'flex-center': 'flex justify-center items-center',
    'flex-col-center': 'flex flex-col justify-center items-center',
    'page-container': 'p-4 w-full h-full overflow-auto',
    'card': 'bg-white rounded shadow p-4 relative transition-all duration-300 hover:shadow-lg card-glow',
    'card-glow': 'before:content-[""] before:absolute before:inset-[-2px] before:bg-gradient-to-r before:from-blue-400 before:via-blue-500 before:to-blue-600 before:rounded-inherit before:opacity-0 before:z-[-1] before:blur-sm before:transition-opacity before:duration-300 hover:before:opacity-60',
    'btn': 'px-4 py-2 rounded transition-colors',
    'btn-primary': 'bg-blue-500 hover:bg-blue-600 text-white',
    'btn-ghost': 'hover:bg-gray-100',
  },
  theme: {
    colors: {
      primary: 'var(--primary-color)',
      success: 'var(--success-color)',
      warning: 'var(--warning-color)',
      error: 'var(--error-color)',
      info: 'var(--info-color)',
    },
  },
})
