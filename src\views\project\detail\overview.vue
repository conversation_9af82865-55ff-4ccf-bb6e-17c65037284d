<script setup lang="ts">
import { 
  CheckCircleOutlined, 
  ClockCircleOutlined, 
  ExclamationCircleOutlined, 
  SyncOutlined, 
  CloseCircleOutlined,
  RightOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
  CheckCircleFilled,
  ExclamationCircleFilled
} from '@vicons/antd'

const props = defineProps<{
  projectInfo: any
  taskStats: any
  taskList: any[]
}>()

// 流程步骤
const processSteps = ref([
  {
    id: 1,
    title: '需求确认',
    status: 'completed',
    operator: '张三',
    time: '2023-06-01 10:00',
    description: '已确认项目需求文档',
    progress: 100,
    files: ['需求文档.pdf', '需求确认单.docx']
  },
  {
    id: 2,
    title: 'UI设计',
    status: 'completed',
    operator: '李四',
    time: '2023-06-03 14:30',
    description: '已完成UI设计稿',
    progress: 100,
    files: ['UI设计稿.sketch', '设计规范.pdf']
  },
  {
    id: 3,
    title: '开发',
    status: 'in-progress',
    operator: '王五',
    time: '2023-06-05 09:15',
    description: '开发中，预计6月15日完成',
    progress: 65,
    files: ['开发计划.md', 'API文档.docx']
  },
  {
    id: 4,
    title: '测试',
    status: 'pending',
    operator: '赵六',
    time: '待开始',
    description: '等待开发完成后开始测试',
    progress: 0,
    files: []
  },
  {
    id: 5,
    title: '上线',
    status: 'pending',
    operator: '钱七',
    time: '待定',
    description: '测试通过后安排上线',
    progress: 0,
    files: []
  }
])

// 获取步骤状态
const getStatusInfo = (status: string) => {
  const statusMap = {
    'completed': {
      icon: CheckCircleFilled,
      color: '#52c41a',
      bgColor: '#f6ffed',
      text: '已完成'
    },
    'in-progress': {
      icon: SyncOutlined,
      color: '#1890ff',
      bgColor: '#e6f7ff',
      text: '进行中'
    },
    'pending': {
      icon: ClockCircleOutlined,
      color: '#d9d9d9',
      bgColor: '#fafafa',
      text: '待处理'
    },
    'error': {
      icon: ExclamationCircleFilled,
      color: '#ff4d4f',
      bgColor: '#fff2f0',
      text: '异常'
    }
  }
  return statusMap[status as keyof typeof statusMap] || statusMap.pending
}

// 处理步骤点击
const handleStepClick = (step: any) => {
  console.log('Step clicked:', step)
  // 这里可以添加点击后的处理逻辑
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  return dateStr
}
</script>

<template>
  <n-grid :cols="24" :x-gap="16" class="p-4">
    <n-gi :span="6">
      <n-card title="项目概览" class="mb-4">
        <n-statistic label="项目进度" :value="projectInfo.progress" suffix="%">
          <template #suffix>/100%</template>
        </n-statistic>
        <n-progress
          type="line"
          :percentage="projectInfo.progress"
          :indicator-placement="'inside'"
          processing
          class="mt-2"
        />
        <n-divider />
        <div class="flex flex-col gap-2">
          <div v-for="(item, index) in [
            { label: '开始日期：', value: projectInfo.startDate },
            { label: '结束日期：', value: projectInfo.endDate },
            { 
              label: '负责人：', 
              value: projectInfo.manager,
              isAvatar: true 
            },
            { label: '版本：', value: projectInfo.version },
            { 
              label: '状态：', 
              value: projectInfo.status,
              isTag: true 
            }
          ]" :key="index" class="flex items-center">
            <span class="w-18 flex-shrink-0 text-gray-500">{{ item.label }}</span>
            <span v-if="!item.isTag && !item.isAvatar" class="flex-1 break-all">{{ item.value }}</span>
            <span v-else-if="item.isAvatar" class="flex-1 flex items-center">
              <n-avatar size="small" :src="''" class="mr-2" />
              {{ item.value }}
            </span>
            <n-tag v-else :type="item.value === '进行中' ? 'primary' : 'default'" size="small">
              {{ item.value }}
            </n-tag>
          </div>
        </div>
      </n-card>

      <n-card title="项目成员" size="small" class="mt-4">
        <div class="flex flex-col gap-3">
          <div v-for="i in 3" :key="i" class="flex items-center py-2">
            <n-avatar round size="small" :src="''" class="mr-2" />
            <span>成员 {{ i }}</span>
          </div>
        </div>
      </n-card>
    </n-gi>

    <n-gi :span="12">
      <n-card title="项目流程" class="mb-4">
        <n-timeline class="mx--3">
          <n-timeline-item
            v-for="step in processSteps"
            :key="step.id"
            :type="step.status === 'completed' ? 'success' : step.status === 'in-progress' ? 'info' : 'default'"
            :title="step.title"
            :time="step.time"
            :content="step.description"
            @click="handleStepClick(step)"
            class="px-3 py-2 rounded transition-all duration-300 cursor-pointer hover:bg-gray-50"
            :class="{
              'bg-blue-50': step.status === 'in-progress',
              'text-blue-600': step.status === 'in-progress',
              'font-semibold': step.status === 'in-progress',
              'text-green-600': step.status === 'completed'
            }"
          >
            <template #icon>
              <n-icon :component="getStatusInfo(step.status).icon" :color="getStatusInfo(step.status).color" />
            </template>
            <div class="mt-2">
              <div class="mb-2">
                <n-space size="small" align="center" class="mb-2">
                  <n-tag 
                    size="small" 
                    :bordered="false" 
                    :color="{ 
                      textColor: getStatusInfo(step.status).color, 
                      color: getStatusInfo(step.status).bgColor 
                    }"
                  >
                    <template #icon>
                      <n-icon :component="getStatusInfo(step.status).icon" :color="getStatusInfo(step.status).color" />
                    </template>
                    {{ getStatusInfo(step.status).text }}
                  </n-tag>
                  <span class="text-gray-400 text-xs flex items-center">
                    <n-icon :component="UserOutlined" class="mr-1" /> {{ step.operator }}
                  </span>
                </n-space>
              </div>
              
              <n-progress
                v-if="step.status === 'in-progress'"
                type="line"
                :percentage="step.progress"
                :indicator-placement="'inside'"
                :height="6"
                :border-radius="0"
                :fill-border-radius="0"
                :show-indicator="false"
                :color="getStatusInfo(step.status).color"
                class="my-2"
              />
              
              <div v-if="step.files && step.files.length" class="mt-3">
                <n-divider class="my-3" />
                <div class="flex flex-col gap-1">
                  <div 
                    v-for="(file, index) in step.files" 
                    :key="index" 
                    class="flex items-center px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200 transition-colors"
                  >
                    <n-icon :component="FileTextOutlined" class="mr-1" />
                    <span>{{ file }}</span>
                  </div>
                </div>
              </div>
            </div>
          </n-timeline-item>
        </n-timeline>
      </n-card>
    </n-gi>

    <n-gi :span="6">
     
      <n-card title="任务统计" size="small" class="mb-4">
        <n-statistic label="总任务数" :value="taskStats.total" />
        <n-progress
          type="line"
          :percentage="taskStats.completed / taskStats.total * 100"
          :indicator-placement="'inside'"
          status="success"
          class="mt-2"
        />
        <n-divider />
        <div class="flex flex-col gap-2">
          <div 
            v-for="(stat, index) in [
              { type: 'success', icon: CheckCircleOutlined, label: '已完成', value: taskStats.completed },
              { type: 'primary', icon: SyncOutlined, label: '进行中', value: taskStats.inProgress },
              { type: 'warning', icon: ExclamationCircleOutlined, label: '未开始', value: taskStats.notStarted },
              { type: 'default', icon: ClockCircleOutlined, label: '已延期', value: taskStats.overdue }
            ]" 
            :key="index"
            class="flex items-center justify-between"
          >
            <n-tag :type="stat.type" size="small" round>
              <template #icon>
                <n-icon :component="stat.icon" />
              </template>
              {{ stat.label }}
            </n-tag>
            <span class="font-bold">{{ stat.value }}</span>
          </div>
        </div>
      </n-card>

      <n-card title="最近任务" size="small">
        <div class="flex flex-col">
          <div 
            v-for="task in taskList.slice(0, 5)" 
            :key="task.id" 
            class="py-3 border-b border-gray-100 last:border-0"
          >
            <div class="flex items-center mb-1">
              <n-tag 
                :type="task.status === '已完成' ? 'success' : task.status === '进行中' ? 'primary' : 'default'" 
                size="small"
                class="flex-shrink-0"
              >
                {{ task.status }}
              </n-tag>
              <span class="ml-2 flex-1 truncate">{{ task.name }}</span>
            </div>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span class="flex items-center">
                <n-avatar size="small" :src="task.avatar || ''" class="mr-1" />
                {{ task.assignee }}
              </span>
              <span>截止: {{ task.dueDate }}</span>
            </div>
          </div>
        </div>
      </n-card>
    
    </n-gi>
  </n-grid>
</template>

<style lang="less" scoped>
@import "@/assets/styles/card.less";

/* 所有样式已转换为UnoCSS类名，移除了原有的CSS */

:deep(.n-card) {
  .card-base();
  .card-blue-glow();
}
</style>
