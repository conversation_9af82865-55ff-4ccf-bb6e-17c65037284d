<script setup lang="ts">
import type { Task } from '@/types/task'
import { getIssueTypeInfo, getStatusInfo, getPriorityInfo, formatDate } from '@/types/task'

interface Props {
  tasks: Task[]
  loading?: boolean
  showProjectFilter?: boolean
}

interface Emits {
  (e: 'edit', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'view', task: Task): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showProjectFilter: false
})

const emit = defineEmits<Emits>()

// 事件处理
const handleEdit = (task: Task) => emit('edit', task)
const handleDelete = (task: Task) => emit('delete', task)
const handleView = (task: Task) => emit('view', task)
</script>

<template>
  <div class="list-view">
    <div v-if="tasks.length === 0" class="empty-state">
      <n-empty description="暂无任务" />
    </div>
    
    <div v-else class="task-items">
      <div
        v-for="task in tasks"
        :key="`task-${task.id}`"
        class="task-item"
        @click="handleView(task)"
      >
        <div class="task-content">
          <div class="task-header">
            <div class="task-title">{{ task.title }}</div>
            <div class="task-actions">
              <n-button size="small" text @click.stop="handleEdit(task)">
                编辑
              </n-button>
              <n-button size="small" text type="error" @click.stop="handleDelete(task)">
                删除
              </n-button>
            </div>
          </div>

          <div class="task-meta">
            <div class="task-info">
              <div class="assignee">
                <n-avatar
                  :size="24"
                  :src="task.assigneeAvatar"
                  class="assignee-avatar"
                >
                  {{ task.assignee.charAt(0) }}
                </n-avatar>
                <span class="assignee-name">{{ task.assignee }}</span>
              </div>

              <div class="reporter">
                <span class="label">报告人:</span>
                <span class="reporter-name">{{ task.reporter }}</span>
              </div>

              <div v-if="showProjectFilter && task.projectName" class="project">
                <span class="project-name">{{ task.projectName }}</span>
              </div>
            </div>

            <div class="task-status">
              <n-tag
                :type="getIssueTypeInfo(task.issueType).type"
                size="small"
                class="issue-type-tag"
              >
                {{ getIssueTypeInfo(task.issueType).text }}
              </n-tag>

              <n-tag
                :type="getStatusInfo(task.status).type"
                size="small"
                class="status-tag"
              >
                {{ getStatusInfo(task.status).text }}
              </n-tag>

              <n-tag
                :type="getPriorityInfo(task.priority).type"
                size="small"
                class="priority-tag"
              >
                {{ getPriorityInfo(task.priority).text }}
              </n-tag>
            </div>
          </div>

          <div class="task-time-info">
            <div class="time-item">
              <span class="label">计划时间:</span>
              <span class="time-range">
                {{ formatDate(task.plannedStartTime) }} - {{ formatDate(task.plannedEndTime) }}
              </span>
            </div>
            <div v-if="task.actualStartTime" class="time-item">
              <span class="label">实际时间:</span>
              <span class="time-range">
                {{ formatDate(task.actualStartTime) }} - {{ task.actualEndTime ? formatDate(task.actualEndTime) : '进行中' }}
              </span>
            </div>
            <div v-if="task.duration" class="time-item">
              <span class="label">时长:</span>
              <span class="duration">{{ task.duration }}小时</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.list-view {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.task-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #1890ff, #40a9ff, #69c0ff, #91d5ff);
    border-radius: inherit;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
    filter: blur(8px);
  }
}

.task-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);

  &::before {
    opacity: 0.6;
  }
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.task-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
  flex: 1;
}

.task-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.task-item:hover .task-actions {
  opacity: 1;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.assignee {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-name {
  font-size: 14px;
  color: #666;
}

.reporter {
  display: flex;
  align-items: center;
  gap: 4px;
}

.reporter-name {
  font-size: 12px;
  color: #666;
}

.project-name {
  font-size: 12px;
  color: #999;
  background: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
}

.task-status {
  display: flex;
  gap: 8px;
}

.task-time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.label {
  color: #999;
  min-width: 60px;
}

.time-range, .duration {
  color: #666;
}

.task-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.due-date {
  font-size: 12px;
  color: #999;
}

.progress-bar {
  width: 100%;
}
</style>
