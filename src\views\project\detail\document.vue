<template>
  <div class="flex h-full p-4">
    <!-- 左侧文档列表区域 -->
    <div class="w-[320px] border-r border-gray-200 flex flex-col h-full bg-white shadow-sm">
      <!-- 搜索和视图切换 -->
      <div class="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div class="text-sm font-medium text-gray-500 mb-2">文档管理</div>
        <n-input-group>
          <n-input
            v-model:value="searchText"
            placeholder="搜索文档..."
            clearable
            class="flex-1"
          >
            <template #prefix>
              <n-icon :component="Search" />
            </template>
          </n-input>
          <n-button-group>
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button
                  :type="viewType === 'list' ? 'primary' : 'default'"
                  @click="viewType = 'list'"
                >
                  <template #icon>
                    <n-icon :component="List" />
                  </template>
                </n-button>
              </template>
              列表视图
            </n-tooltip>
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button
                  :type="viewType === 'grid' ? 'primary' : 'default'"
                  @click="viewType = 'grid'"
                >
                  <template #icon>
                    <n-icon :component="Grid" />
                  </template>
                </n-button>
              </template>
              平铺视图
            </n-tooltip>
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button
                  :type="viewType === 'table' ? 'primary' : 'default'"
                  @click="viewType = 'table'"
                >
                  <template #icon>
                    <n-icon :component="GridOutline" />
                  </template>
                </n-button>
              </template>
              表格视图
            </n-tooltip>
          </n-button-group>
        </n-input-group>
      </div>

      <!-- 文档列表 -->
      <div class="flex-1 overflow-auto">
        <n-scrollbar>
          <!-- 列表视图 -->
          <n-list v-if="viewType === 'list'" hoverable clickable class="view-content">
            <n-list-item
              v-for="doc in filteredDocs"
              :key="doc.id"
              @click="selectDoc(doc)"
              :class="[selectedDoc?.id === doc.id ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50', 'cursor-pointer rounded transition-all duration-200']"
              style="border-left: 4px solid transparent;"
            >
              <n-thing>
                <template #header>
                  <div class="text-sm font-medium text-gray-900">{{ doc.name }}</div>
                </template>
                <template #description>
                  <div class="flex items-center gap-2 mt-1">
                    <n-avatar
                      round
                      size="small"
                      :src="doc.avatar"
                      :fallback-src="'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'"
                      class="flex-shrink-0"
                    />
                    <span class="text-gray-500 text-xs">{{ doc.uploader }}</span>
                    <n-divider vertical />
                    <n-tag size="small" type="info" class="!text-xs">{{ doc.category }}</n-tag>
                    <n-tag size="small" :type="getPhaseColor(doc.phase) as any" class="!text-xs">
                      {{ doc.phase }}
                    </n-tag>
                    <div class="ml-auto text-xs text-gray-400">{{ formatDate(doc.updateTime) }}</div>
                  </div>
                </template>
              </n-thing>
            </n-list-item>
          </n-list>
          
          <!-- 平铺视图 -->
          <n-grid v-else-if="viewType === 'grid'" :cols="2" :x-gap="12" :y-gap="12" class="p-2">
            <n-gi v-for="doc in filteredDocs" :key="doc.id">
              <n-card
                :title="doc.name" :title-style="{ fontSize: '0.875rem', fontWeight: '500' }"
                hoverable
                :class="[selectedDoc?.id === doc.id ? 'ring-2 ring-blue-500' : 'hover:shadow-md', 'transition-all duration-200']"
                @click="selectDoc(doc)"
              >
                <template #cover>
                  <div class="h-32 bg-gray-100 flex items-center justify-center">
                    <n-icon :component="DocumentText" :size="48" class="text-blue-300" />
                  </div>
                </template>
                <div class="flex flex-col gap-2 mt-2">
                  <div class="flex items-center gap-2">
                    <n-avatar
                      round
                      size="small"
                      :src="doc.avatar"
                      :fallback-src="'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'"
                      class="flex-shrink-0"
                    />
                    <span class="text-xs text-gray-600">{{ doc.uploader }}</span>
                  </div>
                  <div class="flex flex-wrap gap-1">
                    <n-tag size="small" type="info" class="!text-xs">{{ doc.category }}</n-tag>
                    <n-tag size="small" :type="getPhaseColor(doc.phase) as any" class="!text-xs">
                      {{ doc.phase }}
                    </n-tag>
                  </div>
                  <div class="text-xs text-gray-400">更新于 {{ formatDate(doc.updateTime) }}</div>
                </div>
              </n-card>
            </n-gi>
          </n-grid>
          
          <!-- 表格视图 -->
          <n-data-table
            v-else-if="viewType === 'table'"
            :columns="tableColumns"
            :data="filteredDocs"
            :pagination="pagination"
            :bordered="false"
            :single-line="false"
            class="view-content"
            @update:sorter="handleSorterChange"
          >
            <template #name="{ row }">
              <div class="flex items-center gap-2">
                <n-icon :component="DocumentText" class="text-blue-400" />
                <span class="text-sm">{{ row.name }}</span>
              </div>
            </template>
            <template #uploader="{ row }">
              <div class="flex items-center gap-2">
                <n-avatar
                  round
                  size="small"
                  :src="row.avatar"
                  :fallback-src="'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'"
                />
                <span class="text-sm">{{ row.uploader }}</span>
              </div>
            </template>
            <template #category="{ row }">
              <n-tag size="small" type="info" class="!text-xs">
                {{ row.category }}
              </n-tag>
            </template>
            <template #phase="{ row }">
              <n-tag size="small" :type="getPhaseColor(row.phase) as any" class="!text-xs">
                {{ row.phase }}
              </n-tag>
            </template>
            <template #action="{ row }">
              <n-button-group size="small">
                <n-button text @click.stop="selectDoc(row)">
                  <n-icon :component="EyeOutline" size="16" />
                </n-button>
                <n-button text>
                  <n-icon :component="Download" size="16" />
                </n-button>
                <n-button text>
                  <n-icon :component="ShareSocial" size="16" />
                </n-button>
              </n-button-group>
            </template>
          </n-data-table>
        </n-scrollbar>
      </div>
    </div>

    <!-- 右侧文档预览区域 -->
    <div class="flex-1 flex flex-col h-full bg-white ml-0.5">
      <div v-if="selectedDoc" class="h-full flex flex-col">
        <div class="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">{{ selectedDoc.name }}</h2>
            <n-button-group>
              <n-button secondary type="primary">
                <template #icon>
                  <n-icon :component="Download" />
                </template>
                下载
              </n-button>
              <n-button secondary type="primary">
                <template #icon>
                  <n-icon :component="ShareSocial" />
                </template>
                分享
              </n-button>
            </n-button-group>
          </div>
          <div class="text-sm text-gray-500 mt-1">
            更新于 {{ formatDate(selectedDoc.updateTime) }} · {{ selectedDoc.size }}
          </div>
        </div>
        <div class="flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50">
          <!-- 这里可以集成文档预览组件，例如PDF.js或其他文档预览库 -->
          <div class="h-full flex items-center justify-center bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="text-center">
              <n-icon :component="DocumentText" size="64" class="text-gray-300 mb-4 mx-auto" />
              <p class="text-gray-400">文档预览区域</p>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="h-full flex items-center justify-center text-gray-400 bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="text-center">
          <n-icon :component="DocumentText" size="64" class="mb-4 mx-auto" />
          <p>请从左侧选择文档进行预览</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue';
import {
  Search,
  List,
  Grid,
  GridOutline,
  DocumentText,
  Download,
  ShareSocial,
  EyeOutline,
} from '@vicons/ionicons5';

// 模拟文档数据
const docs = ref([
  {
    id: 1,
    name: '项目需求文档.pdf',
    updateTime: '2025-06-15T10:30:00',
    size: '2.4 MB',
    uploader: '张三',
    category: '需求文档',
    phase: '需求分析',
    avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
  },
  {
    id: 2,
    name: '设计规范.docx',
    updateTime: '2025-06-16T14:20:00',
    size: '1.8 MB',
    uploader: '李四',
    category: '设计文档',
    phase: '设计阶段',
    avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
  },
  {
    id: 3,
    name: '开发计划.xlsx',
    updateTime: '2025-06-17T09:15:00',
    size: '450 KB',
    uploader: '王五',
    category: '计划文档',
    phase: '开发阶段',
    avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
  },
  {
    id: 4,
    name: '测试报告.pdf',
    updateTime: '2025-06-18T16:45:00',
    size: '3.1 MB',
    uploader: '赵六',
    category: '测试文档',
    phase: '测试阶段',
    avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
  },
  {
    id: 5,
    name: '用户手册.docx',
    updateTime: '2025-06-19T11:00:00',
    size: '2.7 MB',
    uploader: '钱七',
    category: '用户文档',
    phase: '交付阶段',
    avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
  },
]);

// 获取阶段标签颜色
const getPhaseColor = (phase: string) => {
  const colors: Record<string, string> = {
    需求分析: 'blue',
    设计阶段: 'green',
    开发阶段: 'purple',
    测试阶段: 'orange',
    交付阶段: 'teal',
  };
  return colors[phase] || 'gray';
};

const viewType = ref<'list' | 'grid' | 'table'>('list');
const searchText = ref('');
const selectedDoc = ref(null);

// 表格分页配置
const pagination = {
  pageSize: 5,
  showSizePicker: true,
  pageSizes: [5, 10, 20],
  showQuickJumper: true,
};

// 表格列定义
const tableColumns = [
  {
    title: '文档名称',
    key: 'name',
    sorter: 'default',
    width: 250,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '上传人',
    key: 'uploader',
    sorter: 'default',
    width: 120,
  },
  {
    title: '类别',
    key: 'category',
    sorter: 'default',
    width: 100,
  },
  {
    title: '阶段',
    key: 'phase',
    sorter: 'default',
    width: 100,
  },
  {
    title: '更新时间',
    key: 'updateTime',
    sorter: (a: any, b: any) => new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime(),
    width: 150,
    render: (row: any) => formatDate(row.updateTime),
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    align: 'center',
  },
];

// 处理排序变化
const handleSorterChange = (sorter: any) => {
  console.log('Sorter changed:', sorter);
  // 这里可以添加排序逻辑
};

// 过滤文档列表
const filteredDocs = computed(() => {
  if (!searchText.value.trim()) return docs.value;
  const search = searchText.value.toLowerCase();
  return docs.value.filter((doc) => doc.name.toLowerCase().includes(search));
});

// 选择文档
const selectDoc = (doc) => {
  selectedDoc.value = doc;
};

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date
    .toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
    .replace(/\//g, '-');
};
</script>

<style scoped>
.view-content {
  height: calc(100vh - 180px);
  min-height: 400px;
}

:deep(.n-data-table) {
  --n-th-font-weight: 500;
  --n-td-padding: 12px 16px;
}

:deep(.n-data-table .n-data-table-th) {
  background-color: #f9fafb;
}

:deep(.n-data-table .n-data-table-td) {
  border-bottom: 1px solid #f3f4f6;
}

:deep(.n-data-table .n-data-table-tr:hover .n-data-table-td) {
  background-color: #f9fafb;
}
:deep(.n-list-item) {
  padding: 8px 12px;
  margin-bottom: 4px;
}

:deep(.n-list-item:hover) {
  background-color: rgba(59, 130, 246, 0.08);
  box-shadow:
    0 2px 8px rgba(24, 144, 255, 0.15),
    0 0 0 3px rgba(24, 144, 255, 0.1),
    0 0 20px rgba(24, 144, 255, 0.15);
  transform: translateY(-1px);
  border-radius: 8px;
}

/* 文档网格卡片悬停效果 */
:deep(.n-card.hover\\:shadow-md:hover) {
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 0 0 3px rgba(24, 144, 255, 0.1),
    0 0 20px rgba(24, 144, 255, 0.15) !important;
  transform: translateY(-2px);
  border-color: rgba(24, 144, 255, 0.3);
}

/* 自定义滚动条 */
:deep(::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}
</style>